import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/firebase_options.dart';
import 'package:emartdriver/main.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:geolocator/geolocator.dart';

// Callback para inicializar o TaskHandler
@pragma('vm:entry-point')
void startStatusCallback() {
  FlutterForegroundTask.setTaskHandler(StatusTaskHandler());
}

// TaskHandler para gerenciar o status do entregador em background
class StatusTaskHandler extends TaskHandler {
  static const String _notificationTitle = 'Taliso - Status do Entregador';

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    } catch (e) {}

    FlutterForegroundTask.sendDataToMain({
      'action': 'task_started',
      'timestamp': timestamp.millisecondsSinceEpoch,
    });
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    _updateStatusAndLocation();
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    print(
        'StatusTaskHandler destruído: ${timestamp.toString()}, timeout: $isTimeout');

    // Notificar a UI que o serviço foi parado
    FlutterForegroundTask.sendDataToMain({
      'action': 'task_destroyed',
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isTimeout': isTimeout,
    });
  }

  @override
  void onReceiveData(Object data) {
    if (data is Map<String, dynamic>) {
      final action = data['action'] as String?;

      switch (action) {
        case 'update_status':
          final isOnline = data['isOnline'] as bool? ?? false;
          _updateOnlineStatus(isOnline);
          break;
        case 'update_location':
          _updateLocation();
          break;
        case 'force_update':
          _updateStatusAndLocation();
          break;
      }
    }
  }

  @override
  void onNotificationButtonPressed(String id) {
    switch (id) {
      case 'btn_toggle_status':
        FlutterForegroundTask.sendDataToMain({
          'action': 'stop_service_requested',
        });
        break;
      case 'btn_update_location':
        _updateLocation();
        break;
    }
  }

  @override
  void onNotificationPressed() {
    FlutterForegroundTask.sendDataToMain({
      'action': 'notification_pressed',
    });
  }

  Future<void> _updateStatusAndLocation() async {
    try {
      await _updateLocation();
      await _updateOnlineStatus(null); // Mantém o status atual
    } catch (e) {}
  }

  Future<void> _updateOnlineStatus(bool? isOnline) async {
    try {
      final userId =
          await FlutterForegroundTask.getData(key: 'user_id') as String?;

      if (userId == null || userId.isEmpty) {
        return;
      }

      final currentStatus =
          await FlutterForegroundTask.getData(key: 'is_online') as bool? ??
              false;
      final statusToUpdate = isOnline ?? currentStatus;

      final dados = {
        'entregador_id': userId,
        'lastActive': Timestamp.now(),
        'isOnline': statusToUpdate,
        'updatedAt': Timestamp.now(),
      };

      await FirebaseFirestore.instance
          .collection('delivery_men_status')
          .doc(userId)
          .set(dados, SetOptions(merge: true));

      await FlutterForegroundTask.saveData(
          key: 'is_online', value: statusToUpdate);

      // Atualizar notificação
      final statusText = statusToUpdate ? 'Online' : 'Offline';
      FlutterForegroundTask.updateService(
        notificationTitle: _notificationTitle,
        notificationText:
            'Status: $statusText - Última atualização: ${DateTime.now().toString().substring(11, 19)}',
      );

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'status_updated',
        'isOnline': statusToUpdate,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('Erro ao atualizar status online: $e');
    }
  }

  Future<void> _updateLocation() async {
    try {
      print('Iniciando _updateLocation...');
      final userId =
          await FlutterForegroundTask.getData(key: 'user_id') as String?;
      print('UserId para localização: $userId');

      if (userId == null || userId.isEmpty) {
        print('UserId é nulo ou vazio para localização, retornando...');
        return;
      }

      // Verificar permissões de localização
      print('Verificando permissões de localização...');
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        print('Permissão de localização negada: $permission');
        return;
      }
      print('Permissão de localização OK: $permission');

      print('Obtendo posição atual...');
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );
      print('Posição obtida: ${position.latitude}, ${position.longitude}');

      final dados = {
        'entregador_id': userId,
        'location': {
          'latitude': position.latitude,
          'longitude': position.longitude,
        },
        'lastLocationUpdate': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      };
      print('Dados de localização preparados: $dados');

      print('Enviando localização para Firebase...');
      await FirebaseFirestore.instance
          .collection('delivery_men_status')
          .doc(userId)
          .set(dados, SetOptions(merge: true));
      print('Localização enviada para Firebase com sucesso!');

      // Salvar localização localmente
      await FlutterForegroundTask.saveData(
          key: 'last_latitude', value: position.latitude);
      await FlutterForegroundTask.saveData(
          key: 'last_longitude', value: position.longitude);

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'location_updated',
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('Erro ao atualizar localização: $e');
    }
  }
}

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();

  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  final List<void Function(bool)> _listeners = [];
  final List<void Function(Map<String, dynamic>)> _dataListeners = [];

  bool _isOnline = false;
  bool _isServiceRunning = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  // Configurações do serviço em foreground
  static const int _serviceId = 256;
  static const String _channelId = 'status_entregador_channel';
  static const String _channelName = 'Status do Entregador';
  static const String _channelDescription =
      'Serviço para monitorar o status do entregador em background';

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno() {
    _initializeForegroundTask();
  }

  // Getters
  bool get isOnline => _isOnline;
  bool get isServiceRunning => _isServiceRunning;

  // Inicialização do serviço em foreground
  void _initializeForegroundTask() {
    // Inicializar porta de comunicação
    FlutterForegroundTask.initCommunicationPort();

    // Adicionar callback para receber dados do TaskHandler
    FlutterForegroundTask.addTaskDataCallback(_onReceiveTaskData);

    // Configurar o serviço
    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: _channelId,
        channelName: _channelName,
        channelDescription: _channelDescription,
        onlyAlertOnce: true,
        channelImportance: NotificationChannelImportance.LOW,
        priority: NotificationPriority.LOW,
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: true,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(10000), // 10 segundos
        autoRunOnBoot: false,
        autoRunOnMyPackageReplaced: true,
        allowWakeLock: true,
        allowWifiLock: true,
      ),
    );
  }

  // Callback para receber dados do TaskHandler
  void _onReceiveTaskData(Object data) {
    if (data is Map<String, dynamic>) {
      final action = data['action'] as String?;

      switch (action) {
        case 'status_updated':
          final isOnline = data['isOnline'] as bool? ?? false;
          _isOnline = isOnline;
          _notificarListeners(isOnline);
          break;
        case 'location_updated':
          // Notificar listeners de dados sobre atualização de localização
          _notificarDataListeners(data);
          break;
        case 'task_started':
          _isServiceRunning = true;
          break;
        case 'task_destroyed':
          _isServiceRunning = false;
          break;
        case 'stop_service_requested':
          // Parar o serviço quando solicitado pela notificação
          print(
              'StatusEntregadorService._onReceiveTaskData() - Parando serviço por solicitação do usuário');
          stopForegroundService();
          break;
        case 'notification_pressed':
          // Ação quando a notificação é pressionada
          _notificarDataListeners({'action': 'notification_pressed'});
          break;
      }

      // Notificar todos os listeners de dados
      _notificarDataListeners(data);
    }
  }

  // Inicialização do serviço
  Future<void> initialize() async {
    print('🔧 === INICIALIZANDO STATUS ENTREGADOR SERVICE ===');
    final String id = MyAppState.currentUser?.userID ?? '';
    print('👤 UserID obtido: $id');

    if (id.isEmpty) {
      print('❌ ERRO: UserID vazio, não é possível inicializar');
      print('   MyAppState.currentUser: ${MyAppState.currentUser}');
      return;
    }

    // Salvar ID do usuário para uso no TaskHandler
    print(
        'StatusEntregadorService.initialize() - Salvando UserID no TaskHandler');
    await FlutterForegroundTask.saveData(key: 'user_id', value: id);

    // Cancelar subscription anterior se existir
    print(
        'StatusEntregadorService.initialize() - Cancelando subscription anterior');
    _statusSubscription?.cancel();

    // Configurar listener do Firestore
    print(
        'StatusEntregadorService.initialize() - Configurando listener do Firestore');
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen((doc) {
      final dados = doc.data() as Map<String, dynamic>?;
      final online = dados?['isOnline'] == true;
      print(
          'StatusEntregadorService.initialize() - Listener: Status mudou para $online');
      _isOnline = online;
      _notificarListeners(online);
    });

    // Obter status inicial
    print('StatusEntregadorService.initialize() - Obtendo status inicial');
    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    _isOnline = dados?['isOnline'] == true;
    print('StatusEntregadorService.initialize() - Status inicial: $_isOnline');

    // Salvar status inicial
    print(
        'StatusEntregadorService.initialize() - Salvando status inicial no TaskHandler');
    await FlutterForegroundTask.saveData(key: 'is_online', value: _isOnline);
    print('StatusEntregadorService.initialize() - Concluído com sucesso');
  }

  // Solicitar permissões necessárias
  Future<bool> requestPermissions() async {
    print('StatusEntregadorService.requestPermissions() - Iniciando...');
    try {
      // Verificar permissão de notificação
      print(
          'StatusEntregadorService.requestPermissions() - Verificando permissão de notificação');
      final notificationPermission =
          await FlutterForegroundTask.checkNotificationPermission();
      print(
          'StatusEntregadorService.requestPermissions() - Permissão de notificação: $notificationPermission');

      if (notificationPermission != NotificationPermission.granted) {
        print(
            'StatusEntregadorService.requestPermissions() - Solicitando permissão de notificação');
        await FlutterForegroundTask.requestNotificationPermission();
      }

      if (Platform.isAndroid) {
        // Verificar otimização de bateria
        print(
            'StatusEntregadorService.requestPermissions() - Verificando otimização de bateria');
        if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
          print(
              'StatusEntregadorService.requestPermissions() - Solicitando ignorar otimização de bateria');
          await FlutterForegroundTask.requestIgnoreBatteryOptimization();
        }
      }

      // Verificar permissão de localização
      print(
          'StatusEntregadorService.requestPermissions() - Verificando permissão de localização');
      LocationPermission permission = await Geolocator.checkPermission();
      print(
          'StatusEntregadorService.requestPermissions() - Permissão de localização atual: $permission');

      if (permission == LocationPermission.denied) {
        print(
            'StatusEntregadorService.requestPermissions() - Solicitando permissão de localização');
        permission = await Geolocator.requestPermission();
        print(
            'StatusEntregadorService.requestPermissions() - Nova permissão de localização: $permission');
      }

      final result = permission != LocationPermission.denied &&
          permission != LocationPermission.deniedForever;
      print(
          'StatusEntregadorService.requestPermissions() - Resultado final: $result');
      return result;
    } catch (e) {
      print('StatusEntregadorService.requestPermissions() - Erro: $e');
      return false;
    }
  }

  // Iniciar serviço em foreground
  Future<bool> startForegroundService() async {
    print('StatusEntregadorService.startForegroundService() - Iniciando...');
    try {
      print(
          'StatusEntregadorService.startForegroundService() - Verificando se serviço já está rodando');
      if (await FlutterForegroundTask.isRunningService) {
        print(
            'StatusEntregadorService.startForegroundService() - Serviço já está rodando');
        return true;
      }

      print(
          'StatusEntregadorService.startForegroundService() - Iniciando serviço foreground');
      final result = await FlutterForegroundTask.startService(
        serviceId: _serviceId,
        notificationTitle: 'Taliso - Status do Entregador',
        notificationText: 'Monitorando status em background',
        notificationButtons: [
          const NotificationButton(
            id: 'btn_toggle_status',
            text: 'Parar Processo',
          ),
          const NotificationButton(
            id: 'btn_update_location',
            text: 'Atualizar',
          ),
        ],
        callback: startStatusCallback,
      );

      print(
          'StatusEntregadorService.startForegroundService() - Resultado: $result');
      _isServiceRunning = result is ServiceRequestSuccess;
      print(
          'StatusEntregadorService.startForegroundService() - Serviço rodando: $_isServiceRunning');
      return _isServiceRunning;
    } catch (e) {
      print('StatusEntregadorService.startForegroundService() - Erro: $e');
      return false;
    }
  }

  // Parar serviço em foreground
  Future<bool> stopForegroundService() async {
    try {
      final result = await FlutterForegroundTask.stopService();
      _isServiceRunning = false;

      return result is ServiceRequestSuccess;
    } catch (e) {
      return false;
    }
  }

  // Definir status online/offline
  Future<void> setOnlineStatus(bool online,
      {double? latitude, double? longitude}) async {
    final String id = MyAppState.currentUser?.userID ?? '';

    if (id.isEmpty) {
      return;
    }

    try {
      final Map<String, dynamic> dados = {
        'entregador_id': id,
        'lastActive': Timestamp.now(),
        'isOnline': online,
        'updatedAt': Timestamp.now(),
      };

      if (latitude != null && longitude != null) {
        dados['location'] = {
          'latitude': latitude,
          'longitude': longitude,
        };
      }

      await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));

      _isOnline = online;
      _notificarListeners(online);

      // Salvar no armazenamento local do TaskHandler

      await FlutterForegroundTask.saveData(key: 'is_online', value: online);

      if (online && !_isServiceRunning) {
        final serviceStarted = await startForegroundService();

        if (serviceStarted) {
          print('✅ SERVIÇO FOREGROUND INICIADO COM SUCESSO');
        } else {
          print('❌ FALHA AO INICIAR SERVIÇO FOREGROUND');
        }
      } else if (!online && _isServiceRunning) {
        print('🛑 USUÁRIO FICOU OFFLINE - PARANDO SERVIÇO FOREGROUND');
        await stopForegroundService();
      } else if (online && _isServiceRunning) {
        print('ℹ️ USUÁRIO JÁ ONLINE E SERVIÇO JÁ RODANDO');
      } else {
        print('ℹ️ USUÁRIO OFFLINE E SERVIÇO PARADO');
      }

      if (_isServiceRunning) {
        FlutterForegroundTask.sendDataToTask({
          'action': 'update_status',
          'isOnline': online,
        });
      } else {}
    } catch (e) {}
  }

  // Forçar atualização de localização
  Future<void> updateLocation() async {
    if (_isServiceRunning) {
      FlutterForegroundTask.sendDataToTask({
        'action': 'update_location',
      });
    }
  }

  // Forçar atualização completa (status + localização)
  Future<void> forceUpdate() async {
    if (_isServiceRunning) {
      FlutterForegroundTask.sendDataToTask({
        'action': 'force_update',
      });
    }
  }

  // Obter status do entregador
  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }

    try {
      final doc = await _colecaoStatus.doc(id).get();
      return doc.data() as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }

  // Adicionar listener para mudanças de status
  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  // Remover listener de status
  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  // Adicionar listener para dados do TaskHandler
  void addDataListener(void Function(Map<String, dynamic>) listener) {
    _dataListeners.add(listener);
  }

  // Remover listener de dados
  void removeDataListener(void Function(Map<String, dynamic>) listener) {
    _dataListeners.remove(listener);
  }

  // Verificar se o serviço está rodando
  Future<bool> isServiceActive() async {
    return await FlutterForegroundTask.isRunningService;
  }

  // Obter dados salvos localmente
  Future<T?> getSavedData<T>(String key) async {
    try {
      return await FlutterForegroundTask.getData(key: key) as T?;
    } catch (e) {
      return null;
    }
  }

  // Salvar dados localmente
  Future<void> saveData(String key, Object value) async {
    try {
      await FlutterForegroundTask.saveData(key: key, value: value);
    } catch (e) {}
  }

  // Limpar todos os dados salvos
  Future<void> clearAllData() async {
    try {
      await FlutterForegroundTask.clearAllData();
    } catch (e) {}
  }

  // Dispose do serviço
  void dispose() {
    _statusSubscription?.cancel();
    _listeners.clear();
    _dataListeners.clear();
    FlutterForegroundTask.removeTaskDataCallback(_onReceiveTaskData);
  }

  // Métodos privados para notificação de listeners
  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      try {
        listener(online);
      } catch (e) {}
    }
  }

  void _notificarDataListeners(Map<String, dynamic> data) {
    for (final listener in _dataListeners) {
      try {
        listener(data);
      } catch (e) {}
    }
  }

  // Métodos utilitários para configuração avançada

  // Atualizar configurações do serviço
  Future<void> updateServiceConfiguration({
    int? intervalSeconds,
    bool? autoRunOnBoot,
    bool? allowWakeLock,
    bool? allowWifiLock,
  }) async {
    try {
      if (_isServiceRunning) {
        await FlutterForegroundTask.updateService(
          foregroundTaskOptions: ForegroundTaskOptions(
            eventAction: ForegroundTaskEventAction.repeat(
              (intervalSeconds ?? 10) * 1000,
            ),
            autoRunOnBoot: autoRunOnBoot ?? false,
            autoRunOnMyPackageReplaced: true,
            allowWakeLock: allowWakeLock ?? true,
            allowWifiLock: allowWifiLock ?? true,
          ),
        );
      }
    } catch (e) {}
  }

  // Atualizar texto da notificação
  Future<void> updateNotification({
    String? title,
    String? text,
    List<NotificationButton>? buttons,
  }) async {
    try {
      if (_isServiceRunning) {
        await FlutterForegroundTask.updateService(
          notificationTitle: title,
          notificationText: text,
          notificationButtons: buttons,
        );
      }
    } catch (e) {}
  }

  // Obter estatísticas do serviço
  Future<Map<String, dynamic>> getServiceStats() async {
    try {
      final allData = await FlutterForegroundTask.getAllData();
      final isRunning = await FlutterForegroundTask.isRunningService;

      return {
        'isServiceRunning': isRunning,
        'isOnline': _isOnline,
        'listenersCount': _listeners.length,
        'dataListenersCount': _dataListeners.length,
        'savedData': allData,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'isServiceRunning': false,
        'isOnline': _isOnline,
      };
    }
  }
}
