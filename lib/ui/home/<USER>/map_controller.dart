import 'dart:async';
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:emartdriver/ui/home/<USER>/home_store.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Controller responsável por gerenciar toda a lógica do GoogleMap
class MapController {
  final HomeStore _store;
  GoogleMapController? _mapController;
  StreamSubscription<Position>? _positionStreamSubscription;

  // Callbacks para comunicação com a UI
  Function(LatLng)? onMapTap;
  Function(String)? onError;

  MapController(this._store);

  /// Inicializa o controller do mapa
  Future<void> onMapCreated(GoogleMapController controller) async {
    _mapController = controller;

    try {
      // Carrega o estilo do mapa
      final String mapStyle =
          await rootBundle.loadString('assets/map_style.json');
      await _mapController?.setMapStyle(mapStyle);
    } catch (e) {
      log("Error loading map style: $e");
    }

    // Inicia o monitoramento de posição
    _startLocationTracking();
  }

  /// Inicia o rastreamento de localização
  void _startLocationTracking() async {
    try {
      // Verifica permissões
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          onError?.call('Permissão de localização negada');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        onError?.call('Permissão de localização negada permanentemente');
        return;
      }

      // Obtém posição atual
      final Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final currentLatLng = LatLng(position.latitude, position.longitude);
      _store.setCurrentPosition(currentLatLng);

      // Cria marker do entregador
      await _createDeliveryPersonMarker(currentLatLng);

      // Centraliza o mapa na posição atual
      await _centerOnPosition(currentLatLng);

      // Inicia stream de posição
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      ).listen((Position position) {
        final newPosition = LatLng(position.latitude, position.longitude);
        _updateCurrentPosition(newPosition);
      }, onError: (e) {
        log("Error in position stream: $e");
        onError?.call("Erro no rastreamento de posição: $e");
      });

      _store.setLoading(false);
    } catch (e) {
      log("Error starting location tracking: $e");
      onError?.call("Erro ao iniciar rastreamento: $e");
      _store.setLoading(false);
    }
  }

  /// Atualiza a posição atual do entregador
  void _updateCurrentPosition(LatLng newPosition) {
    _store.setCurrentPosition(newPosition);
    _updateDeliveryPersonMarker(newPosition);
  }

  /// Cria o marker do entregador
  Future<void> _createDeliveryPersonMarker(LatLng position) async {
    try {
      if (_store.deliveryPersonIcon == null) {
        final icon = await _createCustomMarkerIcon();
        _store.setDeliveryPersonIcon(icon);
      }

      final marker = Marker(
        markerId: const MarkerId('entregador'),
        position: position,
        icon: _store.deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
        infoWindow: const InfoWindow(title: 'Sua localização'),
      );

      _store.addMarker('entregador', marker);
    } catch (e) {
      log("Error creating delivery person marker: $e");
    }
  }

  /// Atualiza o marker do entregador
  void _updateDeliveryPersonMarker(LatLng position) {
    final marker = Marker(
      markerId: const MarkerId('entregador'),
      position: position,
      icon: _store.deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
      infoWindow: const InfoWindow(title: 'Sua localização'),
    );

    _store.addMarker('entregador', marker);
  }

  /// Cria ícone customizado para o marker do entregador
  Future<BitmapDescriptor> _createCustomMarkerIcon() async {
    try {
      final ByteData data =
          await rootBundle.load('assets/images/delivery_person.png');
      final ui.Codec codec = await ui.instantiateImageCodec(
        data.buffer.asUint8List(),
        targetWidth: 100,
        targetHeight: 100,
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ByteData? byteData = await frameInfo.image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        return BitmapDescriptor.fromBytes(byteData.buffer.asUint8List());
      }
    } catch (e) {
      log("Error creating custom marker icon: $e");
    }

    return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
  }

  /// Centraliza o mapa em uma posição específica
  Future<void> _centerOnPosition(LatLng position, {double zoom = 17}) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: position, zoom: zoom),
        ),
      );
    }
  }

  /// Centraliza o mapa na posição atual
  Future<void> centerOnCurrentLocation() async {
    final currentPos = _store.currentPositionValue;
    if (currentPos != null) {
      await _centerOnPosition(currentPos);
    }
  }

  /// Aumenta o zoom do mapa
  Future<void> zoomIn() async {
    if (_mapController != null) {
      await _mapController!.animateCamera(CameraUpdate.zoomIn());
    }
  }

  /// Diminui o zoom do mapa
  Future<void> zoomOut() async {
    if (_mapController != null) {
      await _mapController!.animateCamera(CameraUpdate.zoomOut());
    }
  }

  /// Força o reload do mapa
  Future<void> forceMapReload() async {
    final currentPos = _store.currentPositionValue;
    if (currentPos != null) {
      await _centerOnPosition(currentPos);
      _updateDeliveryPersonMarker(currentPos);
    }
  }

  /// Adiciona markers para pedidos disponíveis
  Future<void> addOrderMarkers(List<OrderModel> orders) async {
    final currentPos = _store.currentPositionValue;
    if (currentPos == null) return;

    final newMarkers = Map<String, Marker>.from(_store.markersValue);

    // Remove markers antigos de pedidos (mantém o do entregador)
    newMarkers.removeWhere((key, value) => key.startsWith('order_'));

    for (final order in orders) {
      try {
        final storeLocation =
            _getLatLng(order.vendor.address_store?.location.geoPoint);
        if (storeLocation != null) {
          // Calcula distância
          final distance = calculateDistance(
              currentPos.latitude,
              currentPos.longitude,
              storeLocation.latitude,
              storeLocation.longitude);

          final marker = Marker(
            markerId: MarkerId('order_${order.id}'),
            position: storeLocation,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            infoWindow: InfoWindow(
              title: order.vendor.title,
              snippet: 'Distância: ${distance.toStringAsFixed(1)} km',
            ),
            onTap: () => onMapTap?.call(storeLocation),
          );

          newMarkers['order_${order.id}'] = marker;
        }
      } catch (e) {
        log("Error adding marker for order ${order.id}: $e");
      }
    }

    _store.setMarkers(newMarkers);
  }

  /// Adiciona rota no mapa
  Future<void> addRoute(LatLng start, LatLng end,
      {Color color = Colors.blue}) async {
    try {
      final routePoints = await getRouteCoordinates(start, end);

      // Cria RouteInfo para compatibilidade
      final routeInfo = RouteInfo(
        route: routePoints,
        distance: 0.0, // Pode ser calculado se necessário
        duration: 0.0, // Pode ser calculado se necessário
      );
      _store.setSelectedRouteInfo(routeInfo);

      final polyline = Polyline(
        polylineId: const PolylineId('route'),
        points: routePoints,
        color: color,
        width: 5,
        patterns: [PatternItem.dash(20), PatternItem.gap(10)],
      );

      _store.setPolylines({polyline});

      // Ajusta a câmera para mostrar toda a rota
      await _fitRouteBounds(start, end);
    } catch (e) {
      log("Error adding route: $e");
      onError?.call("Erro ao calcular rota: $e");
    }
  }

  /// Ajusta a câmera para mostrar toda a rota
  Future<void> _fitRouteBounds(LatLng start, LatLng end) async {
    if (_mapController == null) return;

    final bounds = LatLngBounds(
      southwest: LatLng(
        start.latitude < end.latitude ? start.latitude : end.latitude,
        start.longitude < end.longitude ? start.longitude : end.longitude,
      ),
      northeast: LatLng(
        start.latitude > end.latitude ? start.latitude : end.latitude,
        start.longitude > end.longitude ? start.longitude : end.longitude,
      ),
    );

    await _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 100),
    );
  }

  /// Remove a rota do mapa
  void clearRoute() {
    _store.clearPolylines();
    _store.setSelectedRouteInfo(null);
  }

  /// Converte GeoPoint para LatLng
  LatLng? _getLatLng(dynamic geoPoint) {
    if (geoPoint == null) return null;

    try {
      if (geoPoint.latitude != null && geoPoint.longitude != null) {
        return LatLng(geoPoint.latitude, geoPoint.longitude);
      }
    } catch (e) {
      log("Error converting GeoPoint to LatLng: $e");
    }

    return null;
  }

  /// Limpa todos os recursos
  void dispose() {
    _positionStreamSubscription?.cancel();
    _mapController?.dispose();
  }
}
