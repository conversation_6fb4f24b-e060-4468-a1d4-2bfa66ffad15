import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

/// Widget para exibir modal de devolução pendente
class DevolucaoPendenteBottomSheet extends StatelessWidget {
  final VoidCallback? onClose;

  const DevolucaoPendenteBottomSheet({
    super.key,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.50,
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          const SizedBox(height: 24),
          Icon(
            Icons.warning_rounded,
            size: 64,
            color: Colors.orange[600],
          ),
          const SizedBox(height: 16),
          const Text(
            'Retorno a Loja',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.orange[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        "Retorno ao Ponto de Coleta",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xff425799),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                const Text(
                  "Esta entrega possui retorno ao Estabelecimento.  Retorne ao estabelecimento para proceder a conclusão da entrega e devolver o item de retorno solicitado.",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content:
                        Text('Aguarde a confirmação do lojista para continuar'),
                    backgroundColor: Color(0xff425799),
                    duration: Duration(seconds: 3),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xff425799),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: const Text(
                'Aguardando Confirmação',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget para exibir modal de retorno à loja
class ReturnBottomSheet extends StatelessWidget {
  final OrderModel returnOrder;
  final Function(OrderModel) onConfirmReturn;

  const ReturnBottomSheet({
    super.key,
    required this.returnOrder,
    required this.onConfirmReturn,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.40,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () {},
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Entrega com Retorno - ${returnOrder.vendor.title}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 15),
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Color.fromARGB(39, 201, 212, 247),
            ),
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/images/caixa.png', height: 30, width: 30),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    "Retorne ao estabelecimento para finalizar a entrega",
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Color.fromARGB(39, 247, 201, 201),
            ),
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                Text(
                  "Loja: ${returnOrder.vendor.title}",
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 5),
                Text(
                  "${returnOrder.vendor.address_store?.logradouro ?? ""}, ${returnOrder.vendor.address_store?.numero ?? ""}, ${returnOrder.vendor.address_store?.bairro ?? ""}, ${returnOrder.vendor.address_store?.cidade ?? ""}",
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () => onConfirmReturn(returnOrder),
                  child: const Text(
                    "Confirmar Devolução",
                    style: TextStyle(fontSize: 14, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Classe utilitária para exibir os bottom sheets
class HomeBottomSheets {
  /// Exibe o modal de devolução pendente
  static void showDevolucaoPendente(
    BuildContext context, {
    VoidCallback? onClose,
  }) {
    showModalBottomSheet(
      enableDrag: false,
      isDismissible: false,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return DevolucaoPendenteBottomSheet(onClose: onClose);
      },
    ).then((_) {
      onClose?.call();
    });
  }

  /// Exibe o modal de retorno à loja
  static void showReturn(
    BuildContext context,
    OrderModel returnOrder, {
    required Function(OrderModel) onConfirmReturn,
  }) {
    showModalBottomSheet(
      enableDrag: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return ReturnBottomSheet(
          returnOrder: returnOrder,
          onConfirmReturn: onConfirmReturn,
        );
      },
    );
  }
}
