import 'dart:async';
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:emartdriver/ui/home/<USER>/gerenciador_modais_pedidos.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/sound_notification.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'controllers/map_controller.dart';
import 'services/order_listener_service.dart';
// Novos imports para componentização
import 'store/home_store.dart';
import 'widgets/home_bottom_sheets.dart';

class HomeScreen extends StatefulWidget {
  final bool isOnline;

  const HomeScreen({super.key, required this.isOnline});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  // Componentes para gerenciamento de estado e lógica
  late final HomeStore _store;
  late final OrderListenerService _orderListenerService;
  late final MapController _mapController;

  // Controller do Google Maps
  GoogleMapController? _googleMapController;

  // Subscriptions para compatibilidade com código existente
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;
  StreamSubscription<Position>? _positionStreamSubscription;
  StreamSubscription<QuerySnapshot>? _devolucaoPendenteSubscription;

  // Ícone customizado para o entregador
  BitmapDescriptor? _deliveryPersonIcon;

  final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';

  @override
  void initState() {
    super.initState();

    // Inicializa os componentes
    _store = HomeStore();
    _mapController = MapController(_store);
    _orderListenerService = OrderListenerService(_store);

    // Configura callbacks do OrderListenerService
    _setupOrderListenerCallbacks();

    // Configura callbacks do MapController
    _setupMapControllerCallbacks();

    // Configura listeners do Store
    _setupStoreListeners();

    // Inicializa estado
    _store.setIsOnline(widget.isOnline);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      globalContext = context;
      verificarDevolucaoPendente();
      iniciarMonitoramentoDevolucaoPendente();
    });

    WidgetsBinding.instance.addObserver(this);
  }

  void _setupOrderListenerCallbacks() {
    _orderListenerService.onAvailableOrdersUpdated = (orders) {
      _mapController.addOrderMarkers(orders);
    };

    _orderListenerService.onAcceptedOrderUpdated = (order) {
      _handleAcceptedOrderUpdate(order);
    };

    _orderListenerService.onReturnOrderUpdated = (order) {
      _handleReturnOrderUpdate(order);
    };

    _orderListenerService.onError = (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(error), backgroundColor: Colors.red),
        );
      }
    };
  }

  void _setupMapControllerCallbacks() {
    _mapController.onMapTap = (position) {
      _onMapTap(position);
    };

    _mapController.onError = (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(error), backgroundColor: Colors.red),
        );
      }
    };
  }

  void _setupStoreListeners() {
    _store.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (didPop) return;

        final temDevolucaoPendente = await temPedidoComDevolucaoPendente();
        if (temDevolucaoPendente) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'Você não pode sair do aplicativo até que a devolução seja confirmada pelo lojista'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );

            if (!_devolucaoBottomsheetAtivo) {
              mostrarBottomsheetDevolucaoPendente();
            }
          }
        } else {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: ValueListenableBuilder<bool>(
          valueListenable: _store.isLoading,
          builder: (context, isLoading, child) {
            if (isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return ValueListenableBuilder<Map<String, Marker>>(
              valueListenable: _store.markers,
              builder: (context, markers, child) {
                return ValueListenableBuilder<Set<Polyline>>(
                  valueListenable: _store.polylines,
                  builder: (context, polylines, child) {
                    return ValueListenableBuilder<LatLng?>(
                      valueListenable: _store.currentPosition,
                      builder: (context, currentPosition, child) {
                        return Stack(
                          children: [
                            GoogleMap(
                              mapType: MapType.terrain,
                              onMapCreated: onMapCreated,
                              myLocationEnabled: false,
                              myLocationButtonEnabled: false,
                              zoomControlsEnabled: false,
                              markers: markers.values.toSet(),
                              polylines: polylines,
                              initialCameraPosition: CameraPosition(
                                target: currentPosition ?? const LatLng(0, 0),
                                zoom: 17,
                              ),
                              onTap: _onMapTap,
                            ),
                            Positioned(
                              right: 5,
                              top: 6,
                              child: Column(
                                children: [
                                  FloatingActionButton(
                                    heroTag: 'zoomInButton',
                                    mini: false,
                                    backgroundColor: Colors.white,
                                    onPressed: zoomIn,
                                    child: const Icon(
                                      Icons.zoom_in_outlined,
                                      color: Color(0xff425799),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  FloatingActionButton(
                                    heroTag: "zoomOutButton",
                                    mini: false,
                                    backgroundColor: Colors.white,
                                    onPressed: zoomOut,
                                    child: const Icon(
                                      Icons.zoom_out_outlined,
                                      color: Color(0xff425799),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  FloatingActionButton(
                                    heroTag: "centerLocationButton",
                                    mini: false,
                                    backgroundColor: Colors.white,
                                    onPressed: centerOnCurrentLocation,
                                    child: const Icon(
                                      Icons.my_location,
                                      color: Color(0xff425799),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  FloatingActionButton(
                                    heroTag: "reloadMapButton",
                                    mini: false,
                                    backgroundColor: Colors.white,
                                    onPressed: forceMapReload,
                                    child: const Icon(
                                      Icons.refresh,
                                      color: Color(0xff425799),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            ValueListenableBuilder<bool>(
                              valueListenable: _store.pedidoAceito,
                              builder: (context, pedidoAceito, child) {
                                return ValueListenableBuilder<OrderModel?>(
                                  valueListenable: _store.selectedOrder,
                                  builder: (context, selectedOrder, child) {
                                    return ValueListenableBuilder<RouteInfo?>(
                                      valueListenable: _store.selectedRouteInfo,
                                      builder:
                                          (context, selectedRouteInfo, child) {
                                        return Positioned(
                                          left: 0,
                                          right: 0,
                                          bottom: 0,
                                          child: PersistentOrderBar(
                                            pedidoAceito: pedidoAceito,
                                            selectedOrder: selectedOrder,
                                            distanceKm: selectedRouteInfo !=
                                                    null
                                                ? (selectedRouteInfo.distance) /
                                                    1000
                                                : null,
                                          ),
                                        );
                                      },
                                    );
                                  },
                                );
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      log("App resumed, reloading map");

      forceMapReload();

      verificarDevolucaoPendente();
    }
  }

  @override
  void didUpdateWidget(HomeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.isOnline != widget.isOnline) {
      _store.setIsOnline(widget.isOnline);
      handleOnlineStatusChange();
    }
  }

  void _onMapTap(LatLng position) {
    // Implementar lógica de tap no mapa se necessário
  }

  void _handleAcceptedOrderUpdate(OrderModel order) {
    // Implementar lógica para pedido aceito
    log("Pedido aceito atualizado: ${order.id}");
  }

  void _handleReturnOrderUpdate(OrderModel order) {
    // Implementar lógica para pedido de devolução
    log("Pedido de devolução atualizado: ${order.id}");
  }

  // Métodos auxiliares para compatibilidade com código existente
  LatLng? get _currentPosition => _store.currentPositionValue;
  bool get _pedidoAceito => _store.pedidoAceitoValue;
  OrderModel? get _selectedOrder => _store.selectedOrderValue;
  RouteInfo? get _selectedRouteInfo => _store.selectedRouteInfoValue;
  Map<String, Marker> get _markers => _store.markersValue;
  Set<Polyline> get _polylines => _store.polylinesValue;

  bool get _devolucaoBottomsheetAtivo => _store.devolucaoBottomsheetAtivoValue;
  bool get _showingNewOrderNotification =>
      _store.showingNewOrderNotificationValue;

  // Métodos auxiliares para modificar o estado

  set _selectedOrder(OrderModel? order) => _store.setSelectedOrder(order);
  set _pedidoAceito(bool aceito) => _store.setPedidoAceito(aceito);
  set _selectedRouteInfo(RouteInfo? routeInfo) =>
      _store.setSelectedRouteInfo(routeInfo);
  set _devolucaoBottomsheetAtivo(bool ativo) =>
      _store.setDevolucaoBottomsheetAtivo(ativo);
  set _currentPosition(LatLng? position) => _store.setCurrentPosition(position);
  set _isLoading(bool loading) => _store.setLoading(loading);
  set _showingNewOrderNotification(bool showing) =>
      _store.setShowingNewOrderNotification(showing);

  @override
  void dispose() {
    // Limpa os componentes
    _orderListenerService.dispose();
    _mapController.dispose();
    _store.dispose();

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void fetchOrders() async {
    if (_currentPosition == null) return;

    if (!widget.isOnline) {
      log("User is offline, not fetching orders");
      setState(() {
        if (!_pedidoAceito) {
          clearMarkersExceptCurrentPosition();
        }
      });
      return;
    }

    try {
      final returnOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      if (returnOrderSnapshot.docs.isNotEmpty) {
        final returnOrder =
            OrderModel.fromJson(returnOrderSnapshot.docs.first.data());

        log("Pedido de devolução encontrado no fetchOrders: ID=${returnOrder.id}");

        LatLng? storePos =
            getLatLng(returnOrder.vendor.address_store?.location.geoPoint);

        if (_currentPosition != null && storePos != null) {
          final returnRoute =
              await getRouteCoordinates(_currentPosition!, storePos);

          setState(() {
            _selectedOrder = returnOrder;
            _pedidoAceito = true;
            _polylines.clear();
            clearMarkersExceptCurrentPosition();

            _polylines.add(Polyline(
              polylineId: const PolylineId("rota_de_retorno"),
              points: returnRoute,
              color: Colors.red,
              width: 5,
            ));

            _markers['destino'] = Marker(
              markerId: const MarkerId('destino'),
              position: storePos,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueGreen),
              infoWindow: InfoWindow(
                  title: "Devolução para: ${returnOrder.vendor.title}",
                  snippet: ""),
              onTap: () {
                if (_pedidoAceito && _selectedOrder != null) {
                  showReturnBottomSheet(returnOrder);
                }
              },
            );
          });

          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              showReturnBottomSheet(returnOrder);
            }
          });

          return;
        }
      }

      final existingOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
            OrderStatus.driverAccepted.description,
            OrderStatus.driverOnTheWay.description
          ])
          .limit(1)
          .get();

      if (existingOrderSnapshot.docs.isNotEmpty) {
        final existingOrder =
            OrderModel.fromJson(existingOrderSnapshot.docs.first.data());

        bool isDeliveryInProgress =
            existingOrder.status == OrderStatus.driverOnTheWay;

        LatLng? destinationPos;
        String destinationTitle;
        String destinationSnippet;
        String routeId;

        if (isDeliveryInProgress) {
          destinationPos = getLatLng(existingOrder.author.shippingAddress
              ?.firstWhere((a) => a.isDefault == true,
                  orElse: () => existingOrder.author.shippingAddress!.first)
              .location
              ?.geoPoint);
          destinationTitle = "Loja: ${existingOrder.vendor.title}";
          destinationSnippet =
              "Entrega para ${existingOrder.author.firstName} ${existingOrder.author.lastName}";
          routeId = "rota_ate_cliente";
        } else {
          destinationPos =
              getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
          destinationTitle = "Loja: ${existingOrder.vendor.title}";
          destinationSnippet = "Coleta de pedido";
          routeId = "rota_ate_loja";
        }

        if (_currentPosition != null && destinationPos != null) {
          final rota =
              await getRouteCoordinates(_currentPosition!, destinationPos);
          _polylines.add(Polyline(
            polylineId: PolylineId(routeId),
            points: rota,
            color: Colors.orange,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: destinationPos,
            icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: destinationTitle, snippet: destinationSnippet),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showStoreInfoDialog(
                  context,
                  _selectedOrder!,
                  aoColetaCancelada: fetchOrders,
                );
              }
            },
          );
        }

        setState(() {
          _selectedOrder = existingOrder;
          _pedidoAceito = true;
        });
      } else {
        final snapshot = await FirebaseFirestore.instance
            .collection(ORDERS)
            .where('status', isEqualTo: OrderStatus.driverSearching.description)
            .get();

        Map<String, List<OrderModel>> pedidosPorLoja = {};

        for (var doc in snapshot.docs) {
          final data = doc.data();
          final order = OrderModel.fromJson(data);

          LatLng? lojaPos =
              getLatLng(order.vendor.address_store?.location.geoPoint);

          if (lojaPos != null && _currentPosition != null) {
            double distanceInKm = calculateDistance(
                _currentPosition!.latitude,
                _currentPosition!.longitude,
                lojaPos.latitude,
                lojaPos.longitude);

            if (distanceInKm <= maxStoreDistanceInKM) {
              String lojaId = order.vendor.id;

              if (!pedidosPorLoja.containsKey(lojaId)) {
                pedidosPorLoja[lojaId] = [];
              }
              pedidosPorLoja[lojaId]!.add(order);
            } else {
              log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} exceeds limit of $maxStoreDistanceInKM");
            }
          }
        }

        pedidosPorLoja.forEach((lojaId, pedidos) {
          if (pedidos.isNotEmpty) {
            final primeiroPedido = pedidos.first;
            LatLng? lojaPos = getLatLng(
                primeiroPedido.vendor.address_store?.location.geoPoint);

            if (lojaPos != null) {
              String titulo = pedidos.length == 1
                  ? "Loja: ${primeiroPedido.vendor.title}"
                  : "Loja: ${primeiroPedido.vendor.title} (${pedidos.length} pedidos)";

              _markers['loja_$lojaId'] = Marker(
                markerId: MarkerId('loja_$lojaId'),
                position: lojaPos,
                icon: BitmapDescriptor.defaultMarkerWithHue(pedidos.length > 1
                    ? BitmapDescriptor.hueOrange
                    : BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(title: titulo, snippet: ""),
                onTap: () {
                  if (pedidos.length == 1) {
                    selectOrderAndShowPanel(pedidos.first);
                  } else {
                    GerenciadorModaisPedidos.exibirSeletorMultiplosPedidos(
                      context: context,
                      pedidos: pedidos,
                      aoSelecionarPedido: selectOrderAndShowPanel,
                    );
                  }
                },
              );
            }
          }
        });

        setState(() {});
      }
    } catch (e) {
      log("Erro ao buscar pedidos: $e");
    }
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  void acceptNewOrder(OrderModel order) async {
    final temDevolucaoPendente = await temPedidoComDevolucaoPendente();
    if (temDevolucaoPendente) {
      mostrarBottomsheetDevolucaoPendente();
      return;
    }

    try {
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(order.id);

      await docRef.update({
        "entregador_id": currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      final updatedDoc = await docRef.get();
      final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

      setState(() {
        _selectedOrder = updatedOrder;
        _pedidoAceito = true;
      });

      listenToAcceptedOrder(_selectedOrder!.id);

      Future.delayed(const Duration(seconds: 1), () {
        Future.delayed(const Duration(milliseconds: 500), () {
          showStoreInfoDialog(
            context,
            updatedOrder,
            aoColetaCancelada: fetchOrders,
          );
        });
      });
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Erro ao aceitar o pedido.")),
      );
    }
  }

  void centerOnCurrentLocation() {
    if (_currentPosition == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Aguardando localização...'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      determinePosition();
      return;
    }

    try {
      _googleMapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentPosition!,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );

      updateCurrentPositionMarker();
    } catch (e) {
      log("Erro ao centralizar mapa: $e");
    }
  }

  void checkForAcceptedOrder() async {
    try {
      final returnQuery = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      final returnSnapshot = await returnQuery.get();

      log("Verificando pedidos de devolução: ${returnSnapshot.docs.length} encontrados");

      if (returnSnapshot.docs.isNotEmpty) {
        final orderId = returnSnapshot.docs.first.id;
        log("Pedido de devolução encontrado com ID: $orderId");
        listenToReturnOrder(orderId);
        return;
      }

      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final orderId = snapshot.docs.first.id;
        listenToAcceptedOrder(orderId);
      } else {
        listenToAvailableOrders();
      }
    } catch (e) {
      log("Error checking for accepted orders: $e");
    }
  }

  void clearMarkersExceptCurrentPosition() {
    final currentPositionMarker = _markers['entregador'];
    _markers.clear();
    if (currentPositionMarker != null) {
      _markers['entregador'] = currentPositionMarker;
    }
  }

  Future<void> determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        setState(() => _isLoading = false);
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          setState(() => _isLoading = false);
          return;
        }
      }

      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);
        _isLoading = false;

        updateCurrentPositionMarker();
      });

      log("Current position determined: ${position.latitude}, ${position.longitude}");

      startPositionUpdates();
      startFirebaseListeners();

      if (mounted && _currentPosition != null) {
        _googleMapController?.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition!, 15),
        );
      }
    } catch (e) {
      log("Error determining position: $e");
      setState(() => _isLoading = false);
    }
  }

  void fecharBottomsheetDevolucaoPendente() {
    if (_devolucaoBottomsheetAtivo && mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
      _devolucaoBottomsheetAtivo = false;

      fetchOrders();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Devolução confirmada pelo lojista! Você já pode aceitar novos pedidos.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void forceMapReload() {
    log("Forçando recarregamento do mapa");
    if (mounted) {
      setState(() {
        _markers.clear();
        _polylines.clear();
      });

      determinePosition();
      fetchOrders();

      if (_currentPosition != null) {
        updateCurrentPositionMarker();
      }
    }
  }

  LatLng? getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  void handleAcceptedOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      final existingOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      bool isDeliveryInProgress =
          existingOrder.status == OrderStatus.driverOnTheWay;

      bool statusChangedToOnTheWay = _selectedOrder != null &&
          _selectedOrder!.status != OrderStatus.driverOnTheWay.description &&
          existingOrder.status == OrderStatus.driverOnTheWay.description;

      LatLng? destinationPos;
      String destinationTitle;
      String destinationSnippet;
      String routeId;

      if (isDeliveryInProgress) {
        destinationPos = getLatLng(existingOrder.author.shippingAddress
            ?.firstWhere((a) => a.isDefault == true,
                orElse: () => existingOrder.author.shippingAddress!.first)
            .location
            ?.geoPoint);
        destinationTitle = "Entrega";
        destinationSnippet = "";
        routeId = "rota_ate_cliente";
      } else {
        destinationPos =
            getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
        destinationTitle = "Loja";
        destinationSnippet = "";
        routeId = "rota_ate_loja";
      }

      if (_currentPosition != null && destinationPos != null) {
        final rota =
            await getRouteCoordinates(_currentPosition!, destinationPos);

        setState(() {
          _selectedOrder = existingOrder;
          _pedidoAceito = true;
          _polylines.clear();
          clearMarkersExceptCurrentPosition();

          _polylines.add(Polyline(
            polylineId: PolylineId(routeId),
            points: rota,
            color: Colors.orange,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: destinationPos ?? const LatLng(0, 0),
            icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: destinationTitle, snippet: destinationSnippet),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showStoreInfoDialog(
                  context,
                  _selectedOrder!,
                  aoColetaCancelada: fetchOrders,
                );
              }
            },
          );
        });

        if (statusChangedToOnTheWay) {
          log("Pedido coletado! Mostrando informações do cliente");

          final loja =
              getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
          final cliente = destinationPos;

          if (loja != null) {
            final info1 = await getRouteInfo(loja, cliente);

            setState(() {
              _selectedRouteInfo = RouteInfo(
                route: [],
                distance: info1.distance,
                duration: info1.duration,
              );
            });

            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                showClientBottomSheet(existingOrder);
              }
            });
          }
        }
      }
    } catch (e) {
      log("Error handling accepted order update: $e");
    }
  }

  void handleAvailableOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      if (!widget.isOnline) {
        log("User is offline, not processing order updates");
        setState(() {
          if (!_pedidoAceito) {
            clearMarkersExceptCurrentPosition();
          }
        });
        return;
      }

      setState(() {
        if (!_pedidoAceito) {
          clearMarkersExceptCurrentPosition();
        }
      });

      Map<String, List<OrderModel>> pedidosPorLoja = {};

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final order = OrderModel.fromJson(data);

        LatLng? lojaPos =
            getLatLng(order.vendor.address_store?.location.geoPoint);

        if (lojaPos != null && !_pedidoAceito && _currentPosition != null) {
          double distanceInKm = calculateDistance(_currentPosition!.latitude,
              _currentPosition!.longitude, lojaPos.latitude, lojaPos.longitude);

          if (distanceInKm <= maxStoreDistanceInKM) {
            String lojaId = order.vendor.id;

            if (!pedidosPorLoja.containsKey(lojaId)) {
              pedidosPorLoja[lojaId] = [];
            }
            pedidosPorLoja[lojaId]!.add(order);
          } else {
            log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} exceeds limit of $maxStoreDistanceInKM");
          }
        }
      }

      pedidosPorLoja.forEach((lojaId, pedidos) {
        if (pedidos.isNotEmpty) {
          final primeiroPedido = pedidos.first;
          LatLng? lojaPos =
              getLatLng(primeiroPedido.vendor.address_store?.location.geoPoint);

          if (lojaPos != null) {
            String titulo = pedidos.length == 1
                ? "Loja: ${primeiroPedido.vendor.title}"
                : "Loja: ${primeiroPedido.vendor.title} (${pedidos.length} pedidos)";

            setState(() {
              _markers['loja_$lojaId'] = Marker(
                markerId: MarkerId('loja_$lojaId'),
                position: lojaPos,
                icon: BitmapDescriptor.defaultMarkerWithHue(pedidos.length > 1
                    ? BitmapDescriptor.hueOrange
                    : BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(title: titulo, snippet: ""),
                onTap: () {
                  if (pedidos.length == 1) {
                    selectOrderAndShowPanel(pedidos.first);
                  } else {
                    GerenciadorModaisPedidos.exibirSeletorMultiplosPedidos(
                      context: context,
                      pedidos: pedidos,
                      aoSelecionarPedido: selectOrderAndShowPanel,
                    );
                  }
                },
              );
            });
          }
        }
      });
    } catch (e) {
      log("Error handling available orders update: $e");
    }
  }

  void handleNewOrder(OrderModel order) {
    if (order.status == OrderStatus.driverAccepted.description &&
        order.entregador_id != null &&
        order.entregador_id!.isNotEmpty &&
        order.entregador_id != currentUserId) {
      log("Pedido ${order.id} já foi aceito por outro entregador: ${order.entregador_id}");

      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text("Este pedido já foi aceito por outro entregador"),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 3),
      ));

      return;
    }

    if (!_pedidoAceito && !_showingNewOrderNotification) {
      playSound();

      setState(() {
        _showingNewOrderNotification = true;
      });

      LatLng? storePos =
          getLatLng(order.vendor.address_store?.location.geoPoint);
      double distanceInKm = 0.0;

      if (_currentPosition != null && storePos != null) {
        distanceInKm = calculateDistance(_currentPosition!.latitude,
            _currentPosition!.longitude, storePos.latitude, storePos.longitude);
      }

      showOrderBottomSheet(
        context: context,
        order: order,
        distanceInKm: distanceInKm,
        durationInMinutes: 0.0,
        onAccept: () => acceptNewOrder(order),
        onDecline: () => setState(() {
          _showingNewOrderNotification = false;
        }),
        pedidoAceito: _pedidoAceito,
      );
    }
  }

  void handleOnlineStatusChange() {
    log("Online status changed to: ${widget.isOnline}");

    if (widget.isOnline) {
      if (_currentPosition != null) {
        startFirebaseListeners();

        verificarDevolucaoPendente();
      }
    } else {
      _ordersSubscription?.cancel();
      _acceptedOrderSubscription?.cancel();

      setState(() {
        if (!_pedidoAceito) {
          clearMarkersExceptCurrentPosition();
        }
      });
    }
  }

  void handleReturnOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      log("Processando pedido de devolução...");
      final returnOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      log("Pedido de devolução: ID=${returnOrder.id}, status=${returnOrder.status}, has_return=${returnOrder.has_return}");

      if (returnOrder.status != OrderStatus.delivered ||
          !returnOrder.has_return) {
        log("Pedido não está mais no status 'delivered' ou não tem has_return=true");
        checkForAcceptedOrder();
        return;
      }

      LatLng? storePos =
          getLatLng(returnOrder.vendor.address_store?.location.geoPoint);

      if (_currentPosition != null && storePos != null) {
        final returnRoute =
            await getRouteCoordinates(_currentPosition!, storePos);

        setState(() {
          _selectedOrder = returnOrder;
          _pedidoAceito = true;
          _polylines.clear();
          clearMarkersExceptCurrentPosition();

          _polylines.add(Polyline(
            polylineId: const PolylineId("rota_de_retorno"),
            points: returnRoute,
            color: Colors.red,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: storePos,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: "Devolução para: ${returnOrder.vendor.title}",
                snippet: ""),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showReturnBottomSheet(returnOrder);
              }
            },
          );
        });
      }
    } catch (e) {
      log("Error handling return order update: $e");
    }
  }

  void iniciarMonitoramentoDevolucaoPendente() {
    _devolucaoPendenteSubscription?.cancel();

    _devolucaoPendenteSubscription = FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: currentUserId)
        .where('has_return', isEqualTo: true)
        .snapshots()
        .listen((snapshot) {
      if (!mounted) return;

      final pedidosDevolucaoPendente = snapshot.docs.where((doc) {
        final data = doc.data();
        final status = data['status'] as String?;
        return status == OrderStatus.returned.description;
      }).toList();

      final temDevolucaoPendente = pedidosDevolucaoPendente.isNotEmpty;

      log("Monitoramento devolução: temDevolucaoPendente=$temDevolucaoPendente, total_has_return=${snapshot.docs.length}, pendentes=${pedidosDevolucaoPendente.length}");

      if (temDevolucaoPendente && !_devolucaoBottomsheetAtivo) {
        log("Exibindo bottomsheet de devolução pendente");
        mostrarBottomsheetDevolucaoPendente();
      } else if (!temDevolucaoPendente && _devolucaoBottomsheetAtivo) {
        log("Fechando bottomsheet - devolução resolvida");
        fecharBottomsheetDevolucaoPendente();
      }
    }, onError: (error) {
      log("Erro no monitoramento de devolução: $error");
    });
  }

  void listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        handleAcceptedOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            handleAcceptedOrderUpdate(docSnapshot);
          } else {
            listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
        });
      } else {
        listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
      listenToAvailableOrders();
    }
  }

  void listenToAvailableOrders() async {
    _acceptedOrderSubscription?.cancel();

    if (!widget.isOnline) {
      setState(() {
        if (!_pedidoAceito) {
          clearMarkersExceptCurrentPosition();
        }
      });
      return;
    }

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      final snapshot = await query.get();

      handleAvailableOrdersUpdate(snapshot);

      _ordersSubscription = query.snapshots().listen((querySnapshot) {
        handleAvailableOrdersUpdate(querySnapshot);
      }, onError: (e) {
        log("Error listening to available orders: $e");
      });
    } catch (e) {
      log("Error fetching initial available orders: $e");
    }
  }

  void listenToReturnOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        handleReturnOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            handleReturnOrderUpdate(docSnapshot);
          } else {
            listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to return order: $e");
        });
      } else {
        listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial return order: $e");
      listenToAvailableOrders();
    }
  }

  Future<void> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      _deliveryPersonIcon = BitmapDescriptor.bytes(markerIcon);

      _deliveryPersonIcon ??=
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);

      setState(() {});
    } catch (e) {
      log("Error in _loadCustomMarkerIcon: $e");

      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  void mostrarBottomsheetDevolucaoPendente() {
    if (_devolucaoBottomsheetAtivo) return;

    _devolucaoBottomsheetAtivo = true;

    HomeBottomSheets.showDevolucaoPendente(
      context,
      onClose: () {
        _devolucaoBottomsheetAtivo = false;
      },
    );
  }

  void onMapCreated(GoogleMapController controller) {
    try {
      _googleMapController = controller;
      if (_currentPosition != null) {
        controller.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition!, 15),
        );

        updateCurrentPositionMarker();
      } else {
        log("Current position is null in _onMapCreated, trying to determine position again");
        determinePosition();
      }
      log("Map controller created successfully");
    } catch (e) {
      log("Error in _onMapCreated: $e");
    }
  }

  void onMapTap(LatLng position) {
    if (_selectedOrder != null && !_pedidoAceito) {
      setState(() {
        _selectedOrder = null;
        _selectedRouteInfo = null;
        _polylines.clear();
        clearMarkersExceptCurrentPosition();
        fetchOrders();
      });
    }
  }

  Future selectOrderAndShowPanel(OrderModel order) async {
    final loja = getLatLng(order.vendor.address_store?.location.geoPoint);
    final cliente = getLatLng(order.author.shippingAddress
        ?.firstWhere((a) => a.isDefault == true)
        .location
        ?.geoPoint);

    if (_currentPosition == null || loja == null || cliente == null) return;

    double storeDistanceInKm = calculateDistance(_currentPosition!.latitude,
        _currentPosition!.longitude, loja.latitude, loja.longitude);

    log("Selected order from store at distance: ${storeDistanceInKm.toStringAsFixed(1)}");

    setState(() {
      _selectedOrder = order;
      _selectedRouteInfo = null;
      _polylines.clear();
      clearMarkersExceptCurrentPosition();
      _pedidoAceito = false;
    });

    final routeInfo = await getRouteInfo(loja, cliente);

    final totalDistance = routeInfo.distance;
    final totalDuration = routeInfo.duration;

    final rotaParaLoja = await getRouteCoordinates(_currentPosition!, loja);

    setState(() {
      _selectedRouteInfo = RouteInfo(
        route: [],
        distance: totalDistance,
        duration: totalDuration,
      );

      _polylines.add(Polyline(
        polylineId: const PolylineId("rota_para_loja"),
        points: rotaParaLoja,
        color: Colors.orange,
        width: 5,
      ));

      bool isDeliveryInProgress = order.status == OrderStatus.driverOnTheWay;

      LatLng? destinationPos;
      String destinationTitle;
      String destinationSnippet;

      if (isDeliveryInProgress) {
        destinationPos = cliente;
        destinationTitle =
            "Entrega para: ${order.author.firstName} ${order.author.lastName}";
        destinationSnippet = "";
      } else {
        destinationPos = loja;
        destinationTitle = "Loja: ${order.vendor.title}";
        destinationSnippet = "";
      }

      _markers['destino'] = Marker(
        markerId: const MarkerId('destino'),
        position: destinationPos,
        icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
            ? BitmapDescriptor.hueRed
            : BitmapDescriptor.hueGreen),
        infoWindow:
            InfoWindow(title: destinationTitle, snippet: destinationSnippet),
        onTap: () {
          if (_pedidoAceito && _selectedOrder != null) {
            showStoreInfoDialog(
              context,
              _selectedOrder!,
              aoColetaCancelada: fetchOrders,
            );
          } else {
            showBottomSheet();
          }
        },
      );
    });

    showBottomSheet();
  }

  void showBottomSheet() {
    if (_selectedOrder == null || _selectedRouteInfo == null) return;

    showOrderBottomSheet(
      context: context,
      pedidoAceito: _pedidoAceito,
      order: _selectedOrder!,
      distanceInKm: (_selectedRouteInfo!.distance) / 1000,
      durationInMinutes: (_selectedRouteInfo!.duration) / 60,
      onAccept: () async {
        try {
          final docRef = FirebaseFirestore.instance
              .collection(ORDERS)
              .doc(_selectedOrder!.id);

          await docRef.update({
            "entregador_id": currentUserId,
            "horaAceite": Timestamp.now(),
            "status": OrderStatus.driverAccepted.description,
          });

          final updatedDoc = await docRef.get();
          final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

          setState(() {
            _selectedOrder = updatedOrder;
            _pedidoAceito = true;
          });

          listenToAcceptedOrder(_selectedOrder!.id);

          Future.delayed(const Duration(seconds: 1), () {
            Future.delayed(const Duration(milliseconds: 500), () {
              showStoreInfoDialog(
                context,
                updatedOrder,
                aoColetaCancelada: fetchOrders,
              );
            });
          });
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Erro ao aceitar o pedido.")),
          );
        }
      },
      onDecline: () {
        setState(() {
          _selectedOrder = null;
          _selectedRouteInfo = null;
          _polylines.clear();
          clearMarkersExceptCurrentPosition();
        });
      },
    );
  }

  void showClientBottomSheet(OrderModel order) {
    if (_selectedRouteInfo == null) return;

    showOrderBottomSheet(
      context: context,
      pedidoAceito: true,
      order: order,
      distanceInKm: (_selectedRouteInfo!.distance) / 1000,
      durationInMinutes: (_selectedRouteInfo!.duration) / 60,
      onAccept: () {},
      onDecline: () {},
    );
  }

  void showReturnBottomSheet(OrderModel returnOrder) {
    HomeBottomSheets.showReturn(
      context,
      returnOrder,
      onConfirmReturn: (OrderModel order) async {
        Navigator.pop(context);
        try {
          final docRef =
              FirebaseFirestore.instance.collection(ORDERS).doc(order.id);

          await showProgress(context, 'Confirmando Devolução', false);

          await docRef.update({
            'status': OrderStatus.returned.description,
          });
          setState(() {
            _selectedOrder = null;
            _selectedRouteInfo = null;
            _polylines.clear();
            clearMarkersExceptCurrentPosition();
          });

          fetchOrders();

          await Future.delayed(const Duration(seconds: 2));
          await hideProgress();

          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                content: Text("Devolução confirmada com sucesso!")));

            checkForAcceptedOrder();
          }
        } catch (e) {
          hideProgress();
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Erro ao confirmar devolução: $e")));
          }
        }
      },
    );
  }

  void startFirebaseListeners() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();

    checkForAcceptedOrder();
  }

  void startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen((Position position) {
      if (mounted) {
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          updateCurrentPositionMarker();
        });
      }
    });
  }

  Future<bool> temPedidoComDevolucaoPendente() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('has_return', isEqualTo: true)
          .where('status', isEqualTo: OrderStatus.returned.description)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log("Erro ao verificar pedidos com devolução pendente: $e");
      return false;
    }
  }

  void updateCurrentPositionMarker() {
    if (_currentPosition == null) return;

    _markers['entregador'] = Marker(
      markerId: const MarkerId('entregador'),
      position: _currentPosition!,
      icon: _deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
      rotation: _currentPosition!.latitude,
    );
  }

  Future<void> verificarDevolucaoPendente() async {
    if (!mounted || _devolucaoBottomsheetAtivo) return;

    final temDevolucaoPendente = await temPedidoComDevolucaoPendente();
    if (temDevolucaoPendente) {
      mostrarBottomsheetDevolucaoPendente();
    }
  }

  void zoomIn() {
    try {
      _googleMapController?.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;

        newZoom = newZoom > 20.0 ? 20.0 : newZoom;

        _googleMapController?.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  void zoomOut() {
    try {
      _googleMapController?.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;

        newZoom = newZoom < 2.0 ? 2.0 : newZoom;

        _googleMapController?.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao diminuir zoom: $e");
    }
  }
}
