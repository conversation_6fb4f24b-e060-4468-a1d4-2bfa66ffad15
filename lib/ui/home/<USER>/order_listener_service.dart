import 'dart:async';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:emartdriver/ui/home/<USER>/home_store.dart';

/// Serviço responsável por escutar mudanças nos pedidos
class OrderListenerService {
  final HomeStore _store;
  final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;
  StreamSubscription<QuerySnapshot>? _devolucaoPendenteSubscription;

  // Callbacks para comunicação com a UI
  Function(List<OrderModel>)? onAvailableOrdersUpdated;
  Function(OrderModel)? onAcceptedOrderUpdated;
  Function(OrderModel)? onReturnOrderUpdated;
  Function()? onOrderCompleted;
  Function(String)? onError;

  OrderListenerService(this._store);

  /// Inicia a escuta de pedidos baseado no status online
  void startListening() {
    if (!_store.isOnlineValue) {
      log("User is offline, not starting order listeners");
      _clearMarkersIfNotAccepted();
      return;
    }

    _checkForAcceptedOrders();
  }

  /// Para todas as escutas de pedidos
  void stopListening() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    _devolucaoPendenteSubscription?.cancel();
  }

  /// Verifica se há pedidos aceitos e inicia a escuta apropriada
  Future<void> _checkForAcceptedOrders() async {
    if (currentUserId.isEmpty) return;

    try {
      // Primeiro verifica se há pedido de devolução
      final returnOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      if (returnOrderSnapshot.docs.isNotEmpty) {
        final returnOrder = OrderModel.fromJson(returnOrderSnapshot.docs.first.data());
        log("Pedido de devolução encontrado: ID=${returnOrder.id}");
        _listenToReturnOrder(returnOrder.id);
        return;
      }

      // Verifica se há pedidos aceitos
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final orderId = snapshot.docs.first.id;
        _listenToAcceptedOrder(orderId);
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error checking for accepted orders: $e");
      onError?.call("Erro ao verificar pedidos aceitos: $e");
    }
  }

  /// Escuta mudanças em um pedido aceito específico
  void _listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleAcceptedOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleAcceptedOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
          onError?.call("Erro ao escutar pedido aceito: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
      onError?.call("Erro ao buscar pedido aceito: $e");
      _listenToAvailableOrders();
    }
  }

  /// Escuta pedidos disponíveis
  void _listenToAvailableOrders() async {
    _acceptedOrderSubscription?.cancel();

    if (!_store.isOnlineValue) {
      _clearMarkersIfNotAccepted();
      return;
    }

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      final snapshot = await query.get();
      _handleAvailableOrdersUpdate(snapshot);

      _ordersSubscription = query.snapshots().listen((querySnapshot) {
        _handleAvailableOrdersUpdate(querySnapshot);
      }, onError: (e) {
        log("Error listening to available orders: $e");
        onError?.call("Erro ao escutar pedidos disponíveis: $e");
      });
    } catch (e) {
      log("Error fetching initial available orders: $e");
      onError?.call("Erro ao buscar pedidos disponíveis: $e");
    }
  }

  /// Escuta pedido de devolução
  void _listenToReturnOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleReturnOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleReturnOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to return order: $e");
          onError?.call("Erro ao escutar pedido de devolução: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial return order: $e");
      onError?.call("Erro ao buscar pedido de devolução: $e");
      _listenToAvailableOrders();
    }
  }

  /// Processa atualizações de pedidos disponíveis
  void _handleAvailableOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      final orders = querySnapshot.docs
          .map((doc) => OrderModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      log("Pedidos disponíveis atualizados: ${orders.length} pedidos");
      onAvailableOrdersUpdated?.call(orders);
    } catch (e) {
      log("Error handling available orders update: $e");
      onError?.call("Erro ao processar pedidos disponíveis: $e");
    }
  }

  /// Processa atualizações de pedido aceito
  void _handleAcceptedOrderUpdate(DocumentSnapshot docSnapshot) {
    try {
      final order = OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);
      log("Pedido aceito atualizado: ID=${order.id}, Status=${order.status?.description}");
      
      _store.setSelectedOrder(order);
      _store.setPedidoAceito(true);
      
      onAcceptedOrderUpdated?.call(order);
    } catch (e) {
      log("Error handling accepted order update: $e");
      onError?.call("Erro ao processar pedido aceito: $e");
    }
  }

  /// Processa atualizações de pedido de devolução
  void _handleReturnOrderUpdate(DocumentSnapshot docSnapshot) {
    try {
      final order = OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);
      log("Pedido de devolução atualizado: ID=${order.id}, Status=${order.status?.description}");
      
      _store.setSelectedOrder(order);
      _store.setPedidoAceito(true);
      
      onReturnOrderUpdated?.call(order);
    } catch (e) {
      log("Error handling return order update: $e");
      onError?.call("Erro ao processar pedido de devolução: $e");
    }
  }

  /// Limpa markers se não há pedido aceito
  void _clearMarkersIfNotAccepted() {
    if (!_store.pedidoAceitoValue) {
      _store.clearMarkersExceptCurrentPosition();
    }
  }

  /// Verifica se há pedido com devolução pendente
  Future<bool> temPedidoComDevolucaoPendente() async {
    if (currentUserId.isEmpty) return false;

    try {
      final snapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log("Error checking for pending return: $e");
      return false;
    }
  }

  /// Inicia escuta de devolução pendente
  void startDevolucaoPendenteListener() {
    if (currentUserId.isEmpty) return;

    _devolucaoPendenteSubscription = FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: currentUserId)
        .where('status', isEqualTo: OrderStatus.delivered.description)
        .where('has_return', isEqualTo: true)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.docs.isNotEmpty && !_store.devolucaoBottomsheetAtivoValue) {
        // Notificar que há devolução pendente
        onReturnOrderUpdated?.call(
          OrderModel.fromJson(snapshot.docs.first.data())
        );
      }
    }, onError: (e) {
      log("Error listening to devolucao pendente: $e");
    });
  }

  /// Para escuta de devolução pendente
  void stopDevolucaoPendenteListener() {
    _devolucaoPendenteSubscription?.cancel();
  }

  /// Limpa todos os recursos
  void dispose() {
    stopListening();
    stopDevolucaoPendenteListener();
  }
}
