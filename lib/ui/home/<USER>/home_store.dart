import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';

/// Store para gerenciar o estado da HomeScreen usando ValueNotifier
class HomeStore extends ChangeNotifier {
  // Estado do mapa
  final ValueNotifier<bool> _isLoading = ValueNotifier(true);
  final ValueNotifier<LatLng?> _currentPosition = ValueNotifier(null);
  final ValueNotifier<Map<String, Marker>> _markers = ValueNotifier({});
  final ValueNotifier<Set<Polyline>> _polylines = ValueNotifier({});
  
  // Estado dos pedidos
  final ValueNotifier<OrderModel?> _selectedOrder = ValueNotifier(null);
  final ValueNotifier<RouteInfo?> _selectedRouteInfo = ValueNotifier(null);
  final ValueNotifier<bool> _pedidoAceito = ValueNotifier(false);
  
  // Estado das notificações e UI
  final ValueNotifier<bool> _showingNewOrderNotification = ValueNotifier(false);
  final ValueNotifier<bool> _devolucaoBottomsheetAtivo = ValueNotifier(false);
  
  // Estado do usuário
  final ValueNotifier<bool> _isOnline = ValueNotifier(false);
  
  // Ícone customizado do entregador
  BitmapDescriptor? _deliveryPersonIcon;

  // Getters para os ValueNotifiers
  ValueNotifier<bool> get isLoading => _isLoading;
  ValueNotifier<LatLng?> get currentPosition => _currentPosition;
  ValueNotifier<Map<String, Marker>> get markers => _markers;
  ValueNotifier<Set<Polyline>> get polylines => _polylines;
  ValueNotifier<OrderModel?> get selectedOrder => _selectedOrder;
  ValueNotifier<RouteInfo?> get selectedRouteInfo => _selectedRouteInfo;
  ValueNotifier<bool> get pedidoAceito => _pedidoAceito;
  ValueNotifier<bool> get showingNewOrderNotification => _showingNewOrderNotification;
  ValueNotifier<bool> get devolucaoBottomsheetAtivo => _devolucaoBottomsheetAtivo;
  ValueNotifier<bool> get isOnline => _isOnline;

  // Getters para valores diretos
  bool get isLoadingValue => _isLoading.value;
  LatLng? get currentPositionValue => _currentPosition.value;
  Map<String, Marker> get markersValue => _markers.value;
  Set<Polyline> get polylinesValue => _polylines.value;
  OrderModel? get selectedOrderValue => _selectedOrder.value;
  RouteInfo? get selectedRouteInfoValue => _selectedRouteInfo.value;
  bool get pedidoAceitoValue => _pedidoAceito.value;
  bool get showingNewOrderNotificationValue => _showingNewOrderNotification.value;
  bool get devolucaoBottomsheetAtivoValue => _devolucaoBottomsheetAtivo.value;
  bool get isOnlineValue => _isOnline.value;
  BitmapDescriptor? get deliveryPersonIcon => _deliveryPersonIcon;

  /// Atualiza o status de carregamento
  void setLoading(bool loading) {
    if (_isLoading.value != loading) {
      _isLoading.value = loading;
      notifyListeners();
    }
  }

  /// Atualiza a posição atual
  void setCurrentPosition(LatLng? position) {
    if (_currentPosition.value != position) {
      _currentPosition.value = position;
      notifyListeners();
    }
  }

  /// Atualiza os markers do mapa
  void setMarkers(Map<String, Marker> markers) {
    _markers.value = Map.from(markers);
    notifyListeners();
  }

  /// Adiciona um marker específico
  void addMarker(String key, Marker marker) {
    final currentMarkers = Map<String, Marker>.from(_markers.value);
    currentMarkers[key] = marker;
    _markers.value = currentMarkers;
    notifyListeners();
  }

  /// Remove um marker específico
  void removeMarker(String key) {
    final currentMarkers = Map<String, Marker>.from(_markers.value);
    currentMarkers.remove(key);
    _markers.value = currentMarkers;
    notifyListeners();
  }

  /// Limpa todos os markers exceto o da posição atual
  void clearMarkersExceptCurrentPosition() {
    final currentPositionMarker = _markers.value['entregador'];
    final newMarkers = <String, Marker>{};
    if (currentPositionMarker != null) {
      newMarkers['entregador'] = currentPositionMarker;
    }
    _markers.value = newMarkers;
    notifyListeners();
  }

  /// Atualiza as polylines do mapa
  void setPolylines(Set<Polyline> polylines) {
    _polylines.value = Set.from(polylines);
    notifyListeners();
  }

  /// Limpa todas as polylines
  void clearPolylines() {
    _polylines.value = <Polyline>{};
    notifyListeners();
  }

  /// Atualiza o pedido selecionado
  void setSelectedOrder(OrderModel? order) {
    if (_selectedOrder.value != order) {
      _selectedOrder.value = order;
      notifyListeners();
    }
  }

  /// Atualiza as informações da rota selecionada
  void setSelectedRouteInfo(RouteInfo? routeInfo) {
    if (_selectedRouteInfo.value != routeInfo) {
      _selectedRouteInfo.value = routeInfo;
      notifyListeners();
    }
  }

  /// Atualiza o status de pedido aceito
  void setPedidoAceito(bool aceito) {
    if (_pedidoAceito.value != aceito) {
      _pedidoAceito.value = aceito;
      notifyListeners();
    }
  }

  /// Atualiza o status de notificação de novo pedido
  void setShowingNewOrderNotification(bool showing) {
    if (_showingNewOrderNotification.value != showing) {
      _showingNewOrderNotification.value = showing;
      notifyListeners();
    }
  }

  /// Atualiza o status do bottomsheet de devolução
  void setDevolucaoBottomsheetAtivo(bool ativo) {
    if (_devolucaoBottomsheetAtivo.value != ativo) {
      _devolucaoBottomsheetAtivo.value = ativo;
      notifyListeners();
    }
  }

  /// Atualiza o status online do usuário
  void setIsOnline(bool online) {
    if (_isOnline.value != online) {
      _isOnline.value = online;
      notifyListeners();
    }
  }

  /// Define o ícone customizado do entregador
  void setDeliveryPersonIcon(BitmapDescriptor? icon) {
    _deliveryPersonIcon = icon;
    notifyListeners();
  }

  /// Reseta o estado para valores iniciais
  void reset() {
    _isLoading.value = true;
    _currentPosition.value = null;
    _markers.value = {};
    _polylines.value = {};
    _selectedOrder.value = null;
    _selectedRouteInfo.value = null;
    _pedidoAceito.value = false;
    _showingNewOrderNotification.value = false;
    _devolucaoBottomsheetAtivo.value = false;
    _deliveryPersonIcon = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _isLoading.dispose();
    _currentPosition.dispose();
    _markers.dispose();
    _polylines.dispose();
    _selectedOrder.dispose();
    _selectedRouteInfo.dispose();
    _pedidoAceito.dispose();
    _showingNewOrderNotification.dispose();
    _devolucaoBottomsheetAtivo.dispose();
    _isOnline.dispose();
    super.dispose();
  }
}
